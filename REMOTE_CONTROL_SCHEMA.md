# Remote Control PocketBase Schema

Add these collections to your PocketBase schema to support remote control functionality.

## 1. Remote Commands Collection

**Collection Name:** `remote_commands`

### Fields:
```json
{
  "id": "text (auto-generated)",
  "target_device": "relation (devices)",
  "command": "select (Screenshot, Shutdown, Reboot, Sleep, Lock, Unlock)",
  "status": "select (pending, executed, failed)",
  "output": "text (optional)",
  "created_by": "relation (xtreme_users)",
  "executed_by": "text (optional)",
  "created": "date (auto)",
  "updated": "date (auto)"
}
```

### API Rules:
- **List/Search Rule:** `@request.auth.role = "Admin" || @request.auth.role = "Staff"`
- **View Rule:** `@request.auth.role = "Admin" || @request.auth.role = "Staff"`
- **Create Rule:** `@request.auth.role = "Admin" || @request.auth.role = "Staff"`
- **Update Rule:** `@request.auth.role = "Admin" || @request.auth.role = "Staff"`
- **Delete Rule:** `@request.auth.role = "Admin"`

## 2. Screenshots Collection

**Collection Name:** `screenshots`

### Fields:
```json
{
  "id": "text (auto-generated)",
  "device": "relation (devices)",
  "image": "file (single, max 10MB)",
  "taken_at": "date (auto)",
  "created": "date (auto)",
  "updated": "date (auto)"
}
```

### API Rules:
- **List/Search Rule:** `@request.auth.role = "Admin" || @request.auth.role = "Staff"`
- **View Rule:** `@request.auth.role = "Admin" || @request.auth.role = "Staff"`
- **Create Rule:** `true` (allow clients to upload screenshots)
- **Update Rule:** `@request.auth.role = "Admin"`
- **Delete Rule:** `@request.auth.role = "Admin"`

## 3. Updated Devices Collection

Add these fields to your existing `devices` collection:

### Additional Fields:
```json
{
  "remote_control_enabled": "bool (default: true)",
  "last_screenshot": "date (optional)",
  "last_command": "date (optional)",
  "client_version": "text (optional)"
}
```

## SQL Migration Script

If you're using PocketBase with SQL, here's the migration script:

```sql
-- Create remote_commands table
CREATE TABLE remote_commands (
    id TEXT PRIMARY KEY,
    target_device TEXT NOT NULL,
    command TEXT NOT NULL CHECK (command IN ('Screenshot', 'Shutdown', 'Reboot', 'Sleep', 'Lock', 'Unlock')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'executed', 'failed')),
    output TEXT,
    created_by TEXT NOT NULL,
    executed_by TEXT,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (target_device) REFERENCES devices(id),
    FOREIGN KEY (created_by) REFERENCES xtreme_users(id)
);

-- Create screenshots table
CREATE TABLE screenshots (
    id TEXT PRIMARY KEY,
    device TEXT NOT NULL,
    image TEXT NOT NULL,
    taken_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device) REFERENCES devices(id)
);

-- Add remote control fields to devices table
ALTER TABLE devices ADD COLUMN remote_control_enabled BOOLEAN DEFAULT true;
ALTER TABLE devices ADD COLUMN last_screenshot DATETIME;
ALTER TABLE devices ADD COLUMN last_command DATETIME;
ALTER TABLE devices ADD COLUMN client_version TEXT;

-- Create indexes for better performance
CREATE INDEX idx_remote_commands_device ON remote_commands(target_device);
CREATE INDEX idx_remote_commands_status ON remote_commands(status);
CREATE INDEX idx_screenshots_device ON screenshots(device);
CREATE INDEX idx_screenshots_taken_at ON screenshots(taken_at);
```

## PocketBase Admin UI Setup

1. **Import Schema:**
   - Go to PocketBase Admin UI
   - Navigate to Collections
   - Import the schema or create collections manually

2. **Set File Storage:**
   - Configure file storage for screenshots
   - Set appropriate file size limits (recommended: 10MB max)
   - Enable image optimization if needed

3. **Configure Real-time:**
   - Enable real-time subscriptions for `remote_commands`
   - Enable real-time subscriptions for `screenshots`

## Security Considerations

1. **Command Validation:**
   - Only allow predefined commands
   - Validate command parameters
   - Log all command executions

2. **Access Control:**
   - Restrict command creation to admin/staff only
   - Implement device-specific permissions
   - Rate limit command execution

3. **File Security:**
   - Sanitize uploaded filenames
   - Implement virus scanning for uploads
   - Set appropriate file retention policies

4. **Audit Trail:**
   - Log all remote control activities
   - Track command execution times
   - Monitor failed command attempts

## Usage Examples

### Creating a Screenshot Command
```javascript
const command = await pb.collection('remote_commands').create({
  target_device: 'device_id_here',
  command: 'Screenshot',
  created_by: 'admin_user_id'
})
```

### Monitoring Command Status
```javascript
// Subscribe to command updates
pb.collection('remote_commands').subscribe(command.id, (e) => {
  console.log('Command status:', e.record.status)
  if (e.record.status === 'executed') {
    console.log('Output:', e.record.output)
  }
})
```

### Retrieving Screenshots
```javascript
const screenshots = await pb.collection('screenshots').getList(1, 10, {
  filter: `device = "${deviceId}"`,
  sort: '-taken_at'
})
```

This schema provides a robust foundation for remote control functionality while maintaining security and auditability.
