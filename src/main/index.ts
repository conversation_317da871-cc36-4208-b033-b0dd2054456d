import { app, shell, BrowserWindow, ipc<PERSON><PERSON>, <PERSON>ray, <PERSON>u, screen } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { exec } from 'child_process'
import { promises as fs } from 'fs'
import icon from '../../resources/icon.png?asset'

let mainWindow: BrowserWindow | null = null
let tray: Tray | null = null
let isKioskMode = !is.dev // Only enable kiosk mode in production
let currentClient: any = null

function createWindow(): void {
  // Get primary display dimensions for kiosk mode
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: isKioskMode ? width : 1200,
    height: isKioskMode ? height : 800,
    x: is.dev ? 100 : undefined, // Position window in development
    y: is.dev ? 100 : undefined,
    show: false,
    autoHideMenuBar: true,
    fullscreen: isKioskMode,
    kiosk: isKioskMode, // Only enable kiosk in production
    resizable: !isKioskMode,
    minimizable: true,
    maximizable: !isKioskMode,
    closable: is.dev, // Allow closing in development
    alwaysOnTop: false, // Don't force always on top in development
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // Prevent window from being closed
  mainWindow.on('close', (event) => {
    if (currentClient) {
      // If client is logged in, minimize to tray instead of closing
      event.preventDefault()
      minimizeToTray()
    } else {
      // Allow closing if no client is logged in
      app.quit()
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()
    mainWindow?.focus() // Force focus on the window

    if (isKioskMode) {
      mainWindow?.setFullScreen(true)
    }

    // In development, open DevTools
    if (is.dev) {
      mainWindow?.webContents.openDevTools()
    }

    console.log('Window should be visible now')
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // Disable certain keyboard shortcuts in kiosk mode
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (isKioskMode && !is.dev) {
      // Disable Alt+Tab, Alt+F4, Ctrl+Alt+Del, etc.
      if (
        (input.alt && input.key === 'Tab') ||
        (input.alt && input.key === 'F4') ||
        (input.control && input.alt && input.key === 'Delete') ||
        (input.control && input.shift && input.key === 'Escape') ||
        input.key === 'F11'
      ) {
        event.preventDefault()
      }
    }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

function createTray(): void {
  // Don't create tray if it already exists and is not destroyed
  if (tray && !tray.isDestroyed()) {
    return
  }

  tray = new Tray(icon)

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Gaming Cafe',
      enabled: false
    },
    {
      type: 'separator'
    },
    {
      label: currentClient ? `User: ${currentClient.username}` : 'No active session',
      enabled: false
    },
    {
      label: currentClient ? 'Show Window' : 'Show Login',
      click: () => {
        showWindow()
      }
    },
    {
      type: 'separator'
    },
    {
      label: 'Admin Panel',
      click: () => {
        // TODO: Implement admin panel
        console.log('Admin panel requested')
      }
    },
    {
      type: 'separator'
    },
    {
      label: 'Exit',
      click: () => {
        currentClient = null
        app.quit()
      }
    }
  ])

  tray.setToolTip('Gaming Cafe Management')
  tray.setContextMenu(contextMenu)

  tray.on('double-click', () => {
    showWindow()
  })
}

function showWindow(): void {
  if (mainWindow) {
    mainWindow.show()
    mainWindow.focus()
    mainWindow.webContents.send('show-window')
  }
}

function minimizeToTray(): void {
  if (mainWindow) {
    mainWindow.hide()
  }

  if (!tray) {
    createTray()
  }
}

function updateTrayMenu(): void {
  // Ensure tray exists before trying to update it
  if (!tray) {
    createTray()
  }

  if (tray) {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Gaming Cafe',
        enabled: false
      },
      {
        type: 'separator'
      },
      {
        label: currentClient ? `User: ${currentClient.username}` : 'No active session',
        enabled: false
      },
      {
        label: currentClient ? 'Show Window' : 'Show Login',
        click: () => {
          showWindow()
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Admin Panel',
        click: () => {
          // TODO: Implement admin panel
          console.log('Admin panel requested')
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Exit',
        click: () => {
          currentClient = null
          app.quit()
        }
      }
    ])

    tray.setContextMenu(contextMenu)
  }
}

// IPC Handlers
function setupIpcHandlers(): void {
  // Handle client login
  ipcMain.on('client-logged-in', (_, client) => {
    currentClient = client
    console.log('Client logged in:', client.username)

    // Minimize to tray after login
    setTimeout(() => {
      minimizeToTray()
      // Update tray menu after minimizing to ensure tray exists
      setTimeout(() => {
        updateTrayMenu()
      }, 100)
    }, 2000) // Give user 2 seconds to see the dashboard
  })

  // Handle client logout
  ipcMain.on('client-logged-out', () => {
    currentClient = null
    console.log('Client logged out')

    // Show window again for next login
    showWindow()
    updateTrayMenu()
  })

  // Handle minimize to tray request
  ipcMain.on('minimize-to-tray', () => {
    minimizeToTray()
  })

  // Handle show window request
  ipcMain.on('show-window', () => {
    showWindow()
  })

  // Legacy ping handler
  ipcMain.on('ping', () => console.log('pong'))

  // Remote control handlers
  ipcMain.handle('execute-command', async (_, command: string) => {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error('Command execution error:', error)
          reject(error.message)
        } else {
          resolve(stdout || stderr)
        }
      })
    })
  })

  ipcMain.handle('read-file', async (_, filename: string) => {
    try {
      const buffer = await fs.readFile(filename)
      return buffer
    } catch (error) {
      throw new Error(`Failed to read file: ${error}`)
    }
  })

  ipcMain.handle('delete-file', async (_, filename: string) => {
    try {
      await fs.unlink(filename)
      return true
    } catch (error) {
      console.error('Failed to delete file:', error)
      return false
    }
  })

  ipcMain.handle('get-platform', () => {
    return process.platform
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.gamingcafe.app')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // Setup IPC handlers
  setupIpcHandlers()

  createWindow()

  app.on('activate', function() {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Don't quit if we have a client session active and tray is available
    if (!currentClient) {
      app.quit()
    }
  }
})

// Handle app activation (macOS)
app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  } else {
    showWindow()
  }
})

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  app.quit()
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    if (mainWindow) {
      showWindow()
    }
  })
}

// Security: Prevent new window creation
app.on('web-contents-created', (_, contents) => {
  contents.on('new-window', (event) => {
    event.preventDefault()
  })
})

// Handle certificate errors (for development)
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (is.dev) {
    // In development, ignore certificate errors
    event.preventDefault()
    callback(true)
  } else {
    // In production, use default behavior
    callback(false)
  }
})

// Cleanup on app quit
app.on('before-quit', () => {
  if (tray && !tray.isDestroyed()) {
    tray.destroy()
    tray = null
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
