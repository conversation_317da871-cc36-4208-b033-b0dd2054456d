import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface AdminPanelProps {
  onClose: () => void
}

export default function AdminPanel({ onClose }: AdminPanelProps) {
  const [activeClients] = useState([
    { id: '1', username: 'user1', timeRemaining: 95, machineId: 'PC-001' },
    { id: '2', username: 'user2', timeRemaining: 145, machineId: 'PC-003' },
  ])

  const [systemStats] = useState({
    totalMachines: 10,
    activeSessions: 2,
    totalRevenue: 245.50,
    averageSessionTime: 125
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white">Admin Panel</h1>
        <Button onClick={onClose} variant="outline">
          Close Admin Panel
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="gaming-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Machines</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalMachines}</div>
          </CardContent>
        </Card>

        <Card className="gaming-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">{systemStats.activeSessions}</div>
          </CardContent>
        </Card>

        <Card className="gaming-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">${systemStats.totalRevenue}</div>
          </CardContent>
        </Card>

        <Card className="gaming-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg Session (min)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.averageSessionTime}</div>
          </CardContent>
        </Card>
      </div>

      {/* Active Sessions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle>Active Sessions</CardTitle>
            <CardDescription>Currently logged in clients</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activeClients.map((client) => (
                <div key={client.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div>
                    <p className="font-semibold">{client.username}</p>
                    <p className="text-sm text-muted-foreground">{client.machineId}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{Math.floor(client.timeRemaining / 60)}h {client.timeRemaining % 60}m</p>
                    <div className="flex space-x-2 mt-1">
                      <Button size="sm" variant="outline">Extend</Button>
                      <Button size="sm" variant="destructive">End</Button>
                    </div>
                  </div>
                </div>
              ))}
              {activeClients.length === 0 && (
                <p className="text-center text-muted-foreground py-4">No active sessions</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="announcement">Send Announcement</Label>
                <div className="flex space-x-2">
                  <Input id="announcement" placeholder="Type your message..." />
                  <Button>Send</Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" className="w-full">
                  💻 System Status
                </Button>
                <Button variant="outline" className="w-full">
                  📊 Reports
                </Button>
                <Button variant="outline" className="w-full">
                  👥 User Management
                </Button>
                <Button variant="outline" className="w-full">
                  ⚙️ Settings
                </Button>
              </div>

              <div className="pt-4 border-t">
                <h4 className="font-semibold mb-2">Emergency Actions</h4>
                <div className="space-y-2">
                  <Button variant="destructive" className="w-full">
                    🚨 End All Sessions
                  </Button>
                  <Button variant="destructive" className="w-full">
                    🔒 Lock All Machines
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Machine Status Grid */}
      <Card className="gaming-card mt-6">
        <CardHeader>
          <CardTitle>Machine Status</CardTitle>
          <CardDescription>Overview of all gaming stations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
            {Array.from({ length: 10 }, (_, i) => {
              const machineId = `PC-${(i + 1).toString().padStart(3, '0')}`
              const isActive = activeClients.some(client => client.machineId === machineId)
              
              return (
                <div
                  key={machineId}
                  className={`p-3 rounded-lg text-center text-xs font-semibold ${
                    isActive 
                      ? 'bg-green-500 text-white' 
                      : 'bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  {machineId}
                </div>
              )
            })}
          </div>
          <div className="flex justify-center space-x-4 mt-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Active</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-300 dark:bg-gray-700 rounded"></div>
              <span>Available</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
