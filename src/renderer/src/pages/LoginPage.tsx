import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { AuthService, SessionStorage } from '@/lib/auth'
import { ClientSession } from '@/types'

const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginPageProps {
  onLogin: (clientSession: ClientSession) => void
}

export default function LoginPage({ onLogin }: LoginPageProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  })

  // Check for existing session on component mount
  useEffect(() => {
    const checkExistingSession = async () => {
      const existingSession = SessionStorage.loadSession()
      if (existingSession && await AuthService.validateSession()) {
        // Resume existing session
        onLogin(existingSession)
      } else {
        // Clear invalid session
        SessionStorage.clearSession()
      }
    }

    checkExistingSession()
  }, [onLogin])

  const handleLogin = async (data: LoginFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await AuthService.authenticate(data)

      if (result.success && result.clientSession) {
        // Save session
        SessionStorage.saveSession(result.clientSession)
        onLogin(result.clientSession)
      } else {
        setError(result.error || 'Login failed')
      }
    } catch (err) {
      setError('Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="kiosk-mode login-container">
      <Card className="gaming-card w-full max-w-md mx-4 neon-glow">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Gaming Cafe
          </CardTitle>
          <CardDescription className="text-lg">
            Welcome! Please login to start your session
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleLogin)} className="space-y-6">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter your username" 
                        {...field}
                        className="h-12 text-lg"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input 
                        type="password"
                        placeholder="Enter your password" 
                        {...field}
                        className="h-12 text-lg"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <div className="text-red-500 text-sm text-center bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                  {error}
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full h-12 text-lg font-semibold"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Logging in...</span>
                  </div>
                ) : (
                  'Start Gaming Session'
                )}
              </Button>
            </form>
          </Form>

          <div className="mt-6 text-center text-sm text-muted-foreground space-y-2">
            <p>Need help? Contact the front desk</p>
            <div className="bg-muted/50 p-3 rounded-md text-xs">
              <p className="font-semibold mb-1">Demo Setup Required:</p>
              <p>1. Start PocketBase server</p>
              <p>2. Import the schema</p>
              <p>3. Create client accounts</p>
              <p>4. Set POCKETBASE_URL environment variable</p>
              <p className="text-blue-600 mt-1">Default: http://127.0.0.1:8090</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
