import { NetworkUsage, SystemInfo } from '@/types'

export class NetworkMonitor {
  private static instance: NetworkMonitor
  private isMonitoring = false
  private monitoringInterval: NodeJS.Timeout | null = null
  private callbacks: ((data: SystemInfo) => void)[] = []
  
  // Mock data for demonstration - in production this would use actual system APIs
  private mockData = {
    baseDownloadSpeed: 50, // Mbps
    baseUploadSpeed: 10,   // Mbps
    totalDownload: 0,      // MB
    totalUpload: 0,        // MB
    cpuUsage: 25,          // %
    memoryUsage: 60,       // %
    diskUsage: 45,         // %
  }

  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor()
    }
    return NetworkMonitor.instance
  }

  startMonitoring(callback: (data: SystemInfo) => void): void {
    this.callbacks.push(callback)
    
    if (!this.isMonitoring) {
      this.isMonitoring = true
      this.monitoringInterval = setInterval(() => {
        this.collectSystemData()
      }, 2000) // Update every 2 seconds
    }
  }

  stopMonitoring(callback?: (data: SystemInfo) => void): void {
    if (callback) {
      this.callbacks = this.callbacks.filter(cb => cb !== callback)
    } else {
      this.callbacks = []
    }

    if (this.callbacks.length === 0 && this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
      this.isMonitoring = false
    }
  }

  private collectSystemData(): void {
    // Simulate realistic network usage patterns
    const now = new Date()
    const timeVariation = Math.sin(now.getTime() / 10000) * 0.3 + 0.7 // 0.4 to 1.0 multiplier
    const randomVariation = Math.random() * 0.4 + 0.8 // 0.8 to 1.2 multiplier
    
    // Simulate network usage
    const downloadSpeed = Math.max(0, this.mockData.baseDownloadSpeed * timeVariation * randomVariation)
    const uploadSpeed = Math.max(0, this.mockData.baseUploadSpeed * timeVariation * randomVariation)
    
    // Accumulate total usage (convert from Mbps to MB over 2 seconds)
    this.mockData.totalDownload += (downloadSpeed * 2) / 8 / 1000 // Convert to MB
    this.mockData.totalUpload += (uploadSpeed * 2) / 8 / 1000
    
    // Simulate system resource usage
    this.mockData.cpuUsage = Math.max(10, Math.min(90, 
      this.mockData.cpuUsage + (Math.random() - 0.5) * 10
    ))
    
    this.mockData.memoryUsage = Math.max(30, Math.min(85, 
      this.mockData.memoryUsage + (Math.random() - 0.5) * 5
    ))
    
    this.mockData.diskUsage = Math.max(20, Math.min(95, 
      this.mockData.diskUsage + (Math.random() - 0.5) * 2
    ))

    const networkUsage: NetworkUsage = {
      downloadSpeed: Math.round(downloadSpeed * 100) / 100,
      uploadSpeed: Math.round(uploadSpeed * 100) / 100,
      totalDownload: Math.round(this.mockData.totalDownload * 100) / 100,
      totalUpload: Math.round(this.mockData.totalUpload * 100) / 100,
      timestamp: now
    }

    const systemInfo: SystemInfo = {
      cpuUsage: Math.round(this.mockData.cpuUsage),
      memoryUsage: Math.round(this.mockData.memoryUsage),
      diskUsage: Math.round(this.mockData.diskUsage),
      networkUsage
    }

    // Notify all callbacks
    this.callbacks.forEach(callback => callback(systemInfo))
  }

  // Utility methods for network management
  static formatSpeed(speedMbps: number): string {
    if (speedMbps >= 1000) {
      return `${(speedMbps / 1000).toFixed(1)} Gbps`
    }
    return `${speedMbps.toFixed(1)} Mbps`
  }

  static formatDataUsage(dataMB: number): string {
    if (dataMB >= 1024) {
      return `${(dataMB / 1024).toFixed(2)} GB`
    }
    return `${dataMB.toFixed(1)} MB`
  }

  static getUsageColor(percentage: number): string {
    if (percentage >= 80) return 'text-red-500'
    if (percentage >= 60) return 'text-yellow-500'
    return 'text-green-500'
  }

  // Website blocking functionality (mock implementation)
  static blockedWebsites = [
    'facebook.com',
    'twitter.com',
    'instagram.com',
    'tiktok.com'
  ]

  static isWebsiteBlocked(url: string): boolean {
    return this.blockedWebsites.some(blocked => url.includes(blocked))
  }

  static blockWebsite(domain: string): void {
    if (!this.blockedWebsites.includes(domain)) {
      this.blockedWebsites.push(domain)
      console.log(`Blocked website: ${domain}`)
    }
  }

  static unblockWebsite(domain: string): void {
    this.blockedWebsites = this.blockedWebsites.filter(site => site !== domain)
    console.log(`Unblocked website: ${domain}`)
  }

  // Time management
  static calculateSessionCost(durationMinutes: number, ratePerHour: number = 5): number {
    return Math.round((durationMinutes / 60) * ratePerHour * 100) / 100
  }

  static formatSessionTime(minutes: number): string {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  // Bandwidth limiting (mock implementation)
  static limitBandwidth(downloadLimit: number, uploadLimit: number): void {
    console.log(`Bandwidth limited to: ${downloadLimit} Mbps down, ${uploadLimit} Mbps up`)
    // In a real implementation, this would interface with system networking tools
  }

  static removeBandwidthLimit(): void {
    console.log('Bandwidth limits removed')
    // In a real implementation, this would remove system networking restrictions
  }
}
