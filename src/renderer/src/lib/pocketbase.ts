import PocketBase from 'pocketbase'
import { <PERSON><PERSON>, Customer, Device, SessionLog, Message, RechargePlan } from '@/types'
import config from './config'

// Initialize PocketBase
const pb = new PocketBase(config.pocketbase.url)

export class PocketBaseService {
  static pb = pb

  // Authentication
  static async authenticateClient(
    username: string,
    password: string
  ): Promise<{ client: Client; customer: Customer }> {
    try {
      console.log('Attempting to authenticate client:', username)

      // Authenticate with clients collection
      const authData = await pb.collection('clients').authWithPassword(username, password)
      const client = authData.record as Client
      console.log('Client authentication successful:', client.id)

      // Get customer data
      console.log('Fetching customer data for client:', client.id)
      let customer: Customer

      try {
        customer = (await pb
          .collection('customers')
          .getFirstListItem(`client="${client.id}"`)) as Customer
        console.log('Customer data retrieved:', customer.id)
      } catch (customerError) {
        console.log('Customer not found, creating new customer record for client:', client.id)

        // Create a new customer record if it doesn't exist
        const newCustomerData = {
          wallet: 0,
          type: 'Pre-paid',
          membership: 'Standard',
          client: client.id
        }

        customer = (await pb.collection('customers').create(newCustomerData)) as Customer
        console.log('New customer created:', customer.id)
      }

      return { client, customer }
    } catch (error) {
      console.error('Authentication failed - Full error:', error)

      // Check if it's a PocketBase error with more details
      if (error && typeof error === 'object' && 'data' in error) {
        console.error('PocketBase error data:', error.data)
      }

      // Provide more specific error messages
      if (error && typeof error === 'object' && 'status' in error) {
        if (error.status === 400) {
          throw new Error('Invalid username or password')
        } else if (error.status === 404) {
          throw new Error('User not found or no customer record exists')
        } else if (error.status === 403) {
          throw new Error('Account access denied')
        }
      }

      // Check if the error is related to customer lookup
      const errorMessage = error instanceof Error ? error.message : String(error)
      if (errorMessage.includes('no rows') || errorMessage.includes('not found')) {
        throw new Error('No customer record found for this user. Please contact support.')
      }

      throw new Error('Authentication failed: ' + errorMessage)
    }
  }

  // Session Management
  static async createSession(customerId: string, deviceId: string): Promise<SessionLog> {
    try {
      const sessionData = {
        session_id: `session_${Date.now()}`,
        customer_id: customerId,
        device_id: deviceId,
        start_time: new Date().toISOString(),
        status: 'Active'
      }

      const session = (await pb.collection('session_logs').create(sessionData)) as SessionLog

      // Update device status to occupied
      await pb.collection('devices').update(deviceId, { status: 'Occupied' })

      return session
    } catch (error) {
      console.error('Failed to create session:', error)
      throw new Error('Failed to create session')
    }
  }

  static async endSession(sessionId: string): Promise<SessionLog> {
    try {
      const session = (await pb.collection('session_logs').getOne(sessionId)) as SessionLog

      const endTime = new Date()
      const startTime = new Date(session.start_time)
      const duration = Math.floor((endTime.getTime() - startTime.getTime()) / 60000) // minutes

      // Get device and group info for cost calculation
      const device = (await pb.collection('devices').getOne(session.device_id, {
        expand: 'group'
      })) as Device & { expand: { group: unknown } }

      const cost = this.calculateSessionCost(duration, device.expand.group.price)

      const updatedSession = (await pb.collection('session_logs').update(sessionId, {
        end_time: endTime.toISOString(),
        duration,
        cost,
        status: 'Completed'
      })) as SessionLog

      // Update device status to available
      await pb.collection('devices').update(session.device_id, { status: 'Available' })

      return updatedSession
    } catch (error) {
      console.error('Failed to end session:', error)
      throw new Error('Failed to end session')
    }
  }

  static async extendSession(customerId: string, additionalMinutes: number): Promise<void> {
    try {
      const customer = (await pb.collection('customers').getOne(customerId)) as Customer
      // Check if customer has enough wallet balance for extension
      const extensionCost = this.calculateExtensionCost(additionalMinutes)
      if (customer.wallet < extensionCost) {
        throw new Error('Insufficient wallet balance')
      }

      // Deduct from wallet
      await pb.collection('customers').update(customerId, {
        wallet: customer.wallet - extensionCost
      })

      // Log the extension (you might want to create a separate collection for this)
      console.log(`Session extended by ${additionalMinutes} minutes for customer ${customerId}`)
    } catch (error) {
      console.error('Failed to extend session:', error)
      throw error
    }
  }

  // Device Management
  static async getAvailableDevices(): Promise<Device[]> {
    try {
      const devices = (await pb.collection('devices').getFullList({
        filter: 'status = "Available"',
        expand: 'group'
      })) as Device[]

      return devices
    } catch (error) {
      console.error('Failed to get available devices:', error)
      return []
    }
  }

  static async getDeviceById(deviceId: string): Promise<Device> {
    try {
      const device = (await pb.collection('devices').getOne(deviceId, {
        expand: 'group'
      })) as Device

      return device
    } catch (error) {
      console.error('Failed to get device:', error)
      throw new Error('Device not found')
    }
  }

  // Chat/Messaging
  static async sendMessage(
    senderId: string,
    deviceId: string,
    message: string,
    isFromClient: boolean = true
  ): Promise<Message> {
    try {
      const messageData = {
        sender: senderId,
        device: deviceId,
        message,
        is_from_client: isFromClient,
        is_read: false
      }

      const newMessage = (await pb.collection('message').create(messageData)) as Message
      return newMessage
    } catch (error) {
      console.error('Failed to send message:', error)
      throw new Error('Failed to send message')
    }
  }

  static async getMessages(deviceId: string, limit: number = 50): Promise<Message[]> {
    try {
      const messages = (await pb.collection('message').getList(1, limit, {
        filter: `device = "${deviceId}"`,
        sort: '-created',
        expand: 'sender'
      })) as any

      return messages.items as Message[]
    } catch (error) {
      console.error('Failed to get messages:', error)
      return []
    }
  }

  static async markMessageAsRead(messageId: string): Promise<void> {
    try {
      await pb.collection('message').update(messageId, { is_read: true })
    } catch (error) {
      console.error('Failed to mark message as read:', error)
    }
  }

  // Wallet & Recharge
  static async getRechargePlans(): Promise<RechargePlan[]> {
    try {
      const plans = (await pb.collection('recharge_plans').getFullList({
        filter: 'status = "Active"',
        sort: 'price'
      })) as RechargePlan[]

      return plans
    } catch (error) {
      console.error('Failed to get recharge plans:', error)
      return []
    }
  }

  static async rechargeWallet(customerId: string, planId: string): Promise<void> {
    try {
      const plan = (await pb.collection('recharge_plans').getOne(planId)) as RechargePlan
      const customer = (await pb.collection('customers').getOne(customerId)) as Customer

      // Update wallet
      await pb.collection('customers').update(customerId, {
        wallet: customer.wallet + plan.value
      })

      // Log the recharge
      await pb.collection('recharge_logs').create({
        customer_id: customerId,
        recharge_id: planId,
        recharged_on: new Date().toISOString(),
        available_time: plan.value
      })
    } catch (error) {
      console.error('Failed to recharge wallet:', error)
      throw new Error('Failed to recharge wallet')
    }
  }

  // Utility functions
  static calculateSessionCost(durationMinutes: number, hourlyRate: number): number {
    return Math.round((durationMinutes / 60) * hourlyRate * 100) / 100
  }

  static calculateExtensionCost(additionalMinutes: number, hourlyRate: number = 5): number {
    return Math.round((additionalMinutes / 60) * hourlyRate * 100) / 100
  }

  static formatTime(minutes: number): string {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  // Real-time subscriptions
  static subscribeToMessages(deviceId: string, callback: (message: Message) => void): () => void {
    const unsubscribe = pb.collection('message').subscribe('*', (e) => {
      if (e.record.device === deviceId) {
        callback(e.record as Message)
      }
    })

    return unsubscribe
  }

  static subscribeToDeviceUpdates(
    deviceId: string,
    callback: (device: Device) => void
  ): () => void {
    const unsubscribe = pb.collection('devices').subscribe(deviceId, (e) => {
      callback(e.record as Device)
    })

    return unsubscribe
  }

  // Session validation
  static async validateSession(): Promise<boolean> {
    return pb.authStore.isValid
  }

  static logout(): void {
    pb.authStore.clear()
  }

  static getCurrentUser(): Client | null {
    return pb.authStore.model as Client | null
  }
}
