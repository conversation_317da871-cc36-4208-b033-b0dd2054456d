import { ClientSession } from '@/types'
import { PocketBaseService } from './pocketbase'

export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResult {
  success: boolean
  clientSession?: ClientSession
  error?: string
}

export class AuthService {
  static async authenticate(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      // Authenticate with PocketBase
      const { client, customer } = await PocketBaseService.authenticateClient(
        credentials.username,
        credentials.password
      )

      // Get available devices (for now, assign the first available PC)
      const availableDevices = await PocketBaseService.getAvailableDevices()
      const device = availableDevices.find(d => d.type === 'PC') || availableDevices[0]

      if (!device) {
        return {
          success: false,
          error: 'No available devices'
        }
      }

      // Create session
      const sessionLog = await PocketBaseService.createSession(customer.id, device.id)

      // Calculate time remaining from wallet
      const timeRemaining = Math.floor(customer.wallet) // Assuming wallet is in minutes

      const clientSession: ClientSession = {
        client,
        customer,
        device,
        sessionLog,
        timeRemaining,
        sessionStart: new Date(sessionLog.start_time),
        isActive: true
      }

      return {
        success: true,
        clientSession
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      }
    }
  }

  static async validateSession(): Promise<boolean> {
    return await PocketBaseService.validateSession()
  }

  static async extendSession(customerId: string, additionalMinutes: number): Promise<void> {
    return await PocketBaseService.extendSession(customerId, additionalMinutes)
  }

  static async endSession(sessionId: string): Promise<void> {
    await PocketBaseService.endSession(sessionId)
    PocketBaseService.logout()
  }
}

// Session storage utilities
export class SessionStorage {
  private static readonly SESSION_KEY = 'gaming_cafe_session'

  static saveSession(clientSession: ClientSession): void {
    try {
      localStorage.setItem(this.SESSION_KEY, JSON.stringify({
        ...clientSession,
        sessionStart: clientSession.sessionStart.toISOString()
      }))
    } catch (error) {
      console.error('Failed to save session:', error)
    }
  }

  static loadSession(): ClientSession | null {
    try {
      const sessionData = localStorage.getItem(this.SESSION_KEY)
      if (!sessionData) return null

      const parsed = JSON.parse(sessionData)
      return {
        ...parsed,
        sessionStart: new Date(parsed.sessionStart)
      }
    } catch (error) {
      console.error('Failed to load session:', error)
      return null
    }
  }

  static clearSession(): void {
    try {
      localStorage.removeItem(this.SESSION_KEY)
      PocketBaseService.logout()
    } catch (error) {
      console.error('Failed to clear session:', error)
    }
  }
}
