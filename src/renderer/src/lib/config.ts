// Application configuration
export const config = {
  // PocketBase configuration
  pocketbase: {
    url: 'http://ec2-43-205-232-118.ap-south-1.compute.amazonaws.com',
    // You can change this URL to point to your PocketBase server
    // Examples:
    // url: 'https://your-pocketbase-server.com',
    // url: 'http://192.168.1.100:8090',
  },
  
  // App configuration
  app: {
    name: 'Gaming Cafe Management',
    version: '1.0.0',

    // Development settings
    debug: true,
    
    // Session settings
    defaultSessionTime: 120, // minutes
    sessionExtensionCost: 5, // per hour
    
    // UI settings
    theme: 'dark',
    language: 'en',
  },
  
  // Gaming cafe settings
  cafe: {
    name: 'Gaming Cafe',
    currency: 'USD',
    timezone: 'UTC',
    
    // Pricing
    hourlyRate: 5.00,
    
    // Time limits
    maxSessionTime: 480, // 8 hours
    warningTime: 15, // minutes before session ends
  }
}

export default config
