// PocketBase Collections Types
export interface Client {
  id: string
  username: string
  email: string
  name?: string
  avatar?: string
  role: 'Guest' | 'User'
  verified: boolean
  created: string
  updated: string
}

export interface XtremeUser {
  id: string
  username: string
  email: string
  name?: string
  avatar?: string
  role: 'Admin' | 'Staff'
  verified: boolean
  created: string
  updated: string
}

export interface Customer {
  id: string
  wallet: number
  type: 'Pre-paid' | 'Post-Paid'
  contact?: string
  membership: 'Standard' | 'Member'
  client: string // relation to clients
  created: string
  updated: string
}

export interface Device {
  id: string
  name: string
  type: 'PC' | 'PS' | 'SIM' | 'VR'
  group: string // relation to groups
  mac_address?: string
  ip_address?: string
  status: 'Available' | 'Occupied' | 'Maintainence' | 'Lost' | 'Damaged'
  powerOff: boolean
  reboot: boolean
  lock: boolean
  sleep: boolean
  token?: string
  created: string
  updated: string
}

export interface Group {
  id: string
  name: string
  price: number
  type: 'PC' | 'PS' | 'SIM' | 'VR'
  created: string
  updated: string
}

export interface SessionLog {
  id: string
  session_id: string
  customer_id: string // relation to customers
  device_id: string // relation to devices
  start_time: string
  end_time?: string
  duration?: number
  cost?: number
  status: 'Active' | 'Completed' | 'Paused'
  created: string
  updated: string
}

export interface Message {
  id: string
  sender: string // relation to clients
  device: string // relation to devices
  message: string
  is_from_client: boolean
  is_read: boolean
  created: string
  updated: string
}

export interface RechargePlan {
  id: string
  name: string
  price: number
  value: number // time in minutes
  note?: string
  status: 'Active' | 'Inactive'
  base_rate: number
  total_hours: number
  created: string
  updated: string
}

// App-specific types
export interface AppState {
  isKioskMode: boolean
  currentClient: Client | null
  currentCustomer: Customer | null
  currentDevice: Device | null
  isLoggedIn: boolean
  isMinimized: boolean
  adminMode: boolean
}

export interface NetworkUsage {
  downloadSpeed: number
  uploadSpeed: number
  totalDownload: number
  totalUpload: number
  timestamp: Date
}

export interface SystemInfo {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkUsage: NetworkUsage
}

export interface ClientSession {
  client: Client
  customer: Customer
  device: Device
  sessionLog: SessionLog
  timeRemaining: number // calculated field
  sessionStart: Date
  isActive: boolean
}

export type AppMode = 'login' | 'client' | 'admin' | 'minimized'
