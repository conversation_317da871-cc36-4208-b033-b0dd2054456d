import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { NetworkMonitor } from '@/lib/network-monitor'
import { SystemInfo } from '@/types'

interface SystemMonitorProps {
  isMinimal?: boolean
}

export default function SystemMonitor({ isMinimal = false }: SystemMonitorProps) {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [isConnected, setIsConnected] = useState(true)

  useEffect(() => {
    const monitor = NetworkMonitor.getInstance()
    
    const handleSystemUpdate = (data: SystemInfo) => {
      setSystemInfo(data)
      setIsConnected(true)
    }

    monitor.startMonitoring(handleSystemUpdate)

    // Simulate connection status
    const connectionCheck = setInterval(() => {
      setIsConnected(Math.random() > 0.05) // 95% uptime simulation
    }, 10000)

    return () => {
      monitor.stopMonitoring(handleSystemUpdate)
      clearInterval(connectionCheck)
    }
  }, [])

  if (!systemInfo) {
    return (
      <Card className="gaming-card">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span>Initializing system monitor...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isMinimal) {
    return (
      <Card className="gaming-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">System Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Network:</span>
            <span className={isConnected ? 'text-green-500' : 'text-red-500'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Download:</span>
            <span>{NetworkMonitor.formatSpeed(systemInfo.networkUsage.downloadSpeed)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Upload:</span>
            <span>{NetworkMonitor.formatSpeed(systemInfo.networkUsage.uploadSpeed)}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Network Status */}
      <Card className="gaming-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>🌐</span>
            <span>Network Status</span>
          </CardTitle>
          <CardDescription>Real-time network monitoring</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Connection:</span>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className={isConnected ? 'text-green-500' : 'text-red-500'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Download Speed:</span>
              <span className="font-semibold">
                {NetworkMonitor.formatSpeed(systemInfo.networkUsage.downloadSpeed)}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (systemInfo.networkUsage.downloadSpeed / 100) * 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Upload Speed:</span>
              <span className="font-semibold">
                {NetworkMonitor.formatSpeed(systemInfo.networkUsage.uploadSpeed)}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (systemInfo.networkUsage.uploadSpeed / 20) * 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="pt-2 border-t space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Downloaded:</span>
              <span>{NetworkMonitor.formatDataUsage(systemInfo.networkUsage.totalDownload)}</span>
            </div>
            <div className="flex justify-between">
              <span>Uploaded:</span>
              <span>{NetworkMonitor.formatDataUsage(systemInfo.networkUsage.totalUpload)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Resources */}
      <Card className="gaming-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>💻</span>
            <span>System Resources</span>
          </CardTitle>
          <CardDescription>Hardware usage monitoring</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>CPU Usage:</span>
              <span className={`font-semibold ${NetworkMonitor.getUsageColor(systemInfo.cpuUsage)}`}>
                {systemInfo.cpuUsage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  systemInfo.cpuUsage >= 80 ? 'bg-red-500' : 
                  systemInfo.cpuUsage >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${systemInfo.cpuUsage}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Memory Usage:</span>
              <span className={`font-semibold ${NetworkMonitor.getUsageColor(systemInfo.memoryUsage)}`}>
                {systemInfo.memoryUsage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  systemInfo.memoryUsage >= 80 ? 'bg-red-500' : 
                  systemInfo.memoryUsage >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${systemInfo.memoryUsage}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Disk Usage:</span>
              <span className={`font-semibold ${NetworkMonitor.getUsageColor(systemInfo.diskUsage)}`}>
                {systemInfo.diskUsage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  systemInfo.diskUsage >= 80 ? 'bg-red-500' : 
                  systemInfo.diskUsage >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${systemInfo.diskUsage}%` }}
              ></div>
            </div>
          </div>

          <div className="pt-2 border-t text-xs text-muted-foreground">
            Last updated: {systemInfo.networkUsage.timestamp.toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
