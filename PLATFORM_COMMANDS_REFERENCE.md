# Cross-Platform Command Reference

This document provides comprehensive command references for remote control functionality across Windows, macOS, and Linux.

## Screenshots

### Linux
```bash
# Method 1: scrot (most reliable)
scrot screenshot.png
scrot -s screenshot.png  # Select area
scrot -u screenshot.png  # Current window

# Method 2: gnome-screenshot (GNOME)
gnome-screenshot -f screenshot.png
gnome-screenshot -w -f screenshot.png  # Current window

# Method 3: ImageMagick import
import -window root screenshot.png
import -window $(xdotool getactivewindow) screenshot.png  # Active window

# Method 4: maim (modern alternative)
maim screenshot.png
maim -s screenshot.png  # Select area

# Method 5: Wayland (for newer systems)
grim screenshot.png  # Full screen
grim -g "$(slurp)" screenshot.png  # Select area
```

### Windows
```powershell
# Method 1: PowerShell with .NET (most reliable)
Add-Type -AssemblyName System.Windows.Forms,System.Drawing
$bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
$bmp = New-Object System.Drawing.Bitmap $bounds.width, $bounds.height
$graphics = [System.Drawing.Graphics]::FromImage($bmp)
$graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.size)
$bmp.Save('screenshot.png')
$bmp.Dispose()
$graphics.Dispose()

# Method 2: Using nircmd (requires download)
nircmd.exe savescreenshot screenshot.png

# Method 3: Using PowerShell one-liner
powershell -command "Add-Type -AssemblyName System.Windows.Forms,System.Drawing; $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds; $bmp = New-Object System.Drawing.Bitmap $bounds.width, $bounds.height; $graphics = [System.Drawing.Graphics]::FromImage($bmp); $graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.size); $bmp.Save('screenshot.png'); $bmp.Dispose(); $graphics.Dispose()"

# Method 4: Using Windows built-in (saves to clipboard)
powershell -command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%{PRTSC}')"
```

### macOS
```bash
# Method 1: screencapture (built-in)
screencapture screenshot.png
screencapture -x screenshot.png  # No sound
screencapture -T 5 screenshot.png  # 5 second delay

# Method 2: Specific window
screencapture -w screenshot.png

# Method 3: Interactive selection
screencapture -i screenshot.png

# Method 4: Specific display
screencapture -D 1 screenshot.png
```

## System Control Commands

### Shutdown

#### Linux
```bash
# Immediate shutdown
sudo shutdown -h now
sudo poweroff
systemctl poweroff

# Scheduled shutdown (10 minutes)
sudo shutdown -h +10
sudo shutdown -h 22:30  # At specific time

# Cancel scheduled shutdown
sudo shutdown -c
```

#### Windows
```cmd
# Immediate shutdown
shutdown /s /t 0

# Scheduled shutdown (600 seconds = 10 minutes)
shutdown /s /t 600

# Shutdown with message
shutdown /s /t 60 /c "System will shutdown in 1 minute"

# Cancel scheduled shutdown
shutdown /a

# Force shutdown (kills all processes)
shutdown /s /f /t 0
```

#### macOS
```bash
# Immediate shutdown
sudo shutdown -h now

# Scheduled shutdown (10 minutes)
sudo shutdown -h +10

# At specific time
sudo shutdown -h 2230  # 10:30 PM

# Cancel shutdown
sudo killall shutdown
```

### Reboot

#### Linux
```bash
# Immediate reboot
sudo reboot
sudo shutdown -r now
systemctl reboot

# Scheduled reboot
sudo shutdown -r +10
sudo shutdown -r 22:30
```

#### Windows
```cmd
# Immediate reboot
shutdown /r /t 0

# Scheduled reboot
shutdown /r /t 600

# Force reboot
shutdown /r /f /t 0

# Cancel scheduled reboot
shutdown /a
```

#### macOS
```bash
# Immediate reboot
sudo shutdown -r now

# Scheduled reboot
sudo shutdown -r +10
sudo shutdown -r 2230
```

### Sleep/Suspend

#### Linux
```bash
# Suspend to RAM
systemctl suspend
sudo pm-suspend

# Hibernate (suspend to disk)
systemctl hibernate
sudo pm-hibernate

# Hybrid sleep
systemctl hybrid-sleep
```

#### Windows
```cmd
# Sleep
rundll32.exe powrprof.dll,SetSuspendState 0,1,0

# Hibernate
shutdown /h

# Using PowerShell
powershell -command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Application]::SetSuspendState('Suspend', $false, $false)"
```

#### macOS
```bash
# Sleep
pmset sleepnow

# Display sleep only
pmset displaysleepnow

# Prevent sleep for 1 hour
caffeinate -t 3600
```

### Lock Screen

#### Linux
```bash
# GNOME
gnome-screensaver-command -l
dbus-send --type=method_call --dest=org.gnome.ScreenSaver /org/gnome/ScreenSaver org.gnome.ScreenSaver.Lock

# KDE
qdbus org.kde.screensaver /ScreenSaver Lock
loginctl lock-session

# i3/sway
i3lock
swaylock

# Custom lock (betterlockscreen)
betterlockscreen -l
betterlockscreen -l dim  # With dim effect

# xscreensaver
xscreensaver-command -lock

# Light Display Manager
dm-tool lock
```

#### Windows
```cmd
# Lock workstation
rundll32.exe user32.dll,LockWorkStation

# Using PowerShell
powershell -command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Application]::SetSuspendState('Suspend', $false, $false)"

# Alternative method
%windir%\System32\rundll32.exe user32.dll,LockWorkStation
```

#### macOS
```bash
# Lock screen
/System/Library/CoreServices/Menu\ Extras/User.menu/Contents/Resources/CGSession -suspend

# Alternative methods
osascript -e 'tell application "System Events" to keystroke "q" using {command down, control down}'
pmset displaysleepnow  # Display sleep (similar to lock)

# Using shortcuts
osascript -e 'tell application "System Events" to key code 12 using {control down, command down}'
```

## Advanced Commands

### Process Management

#### Kill Specific Applications
```bash
# Linux
pkill firefox
killall chrome
kill -9 $(pgrep vlc)

# Windows
taskkill /f /im firefox.exe
taskkill /f /im chrome.exe
wmic process where name="vlc.exe" delete

# macOS
pkill Firefox
killall "Google Chrome"
osascript -e 'quit app "VLC"'
```

#### System Information
```bash
# Linux
uname -a          # System info
free -h           # Memory usage
df -h             # Disk usage
top               # Process list
htop              # Better process viewer

# Windows
systeminfo        # System information
wmic computersystem get TotalPhysicalMemory  # RAM
wmic logicaldisk get size,freespace,caption  # Disk space
tasklist          # Process list

# macOS
system_profiler SPSoftwareDataType  # System info
vm_stat           # Memory usage
df -h             # Disk usage
top               # Process list
```

### Network Commands

#### Network Information
```bash
# Linux
ip addr show      # IP addresses
netstat -tuln     # Open ports
ss -tuln          # Modern netstat
iwconfig          # WiFi info

# Windows
ipconfig /all     # IP configuration
netstat -an       # Open ports
netsh wlan show profiles  # WiFi profiles

# macOS
ifconfig          # Network interfaces
netstat -an       # Open ports
airport -I        # WiFi info (requires airport utility)
```

## Error Handling and Fallbacks

### Screenshot Fallbacks
```bash
# Linux fallback chain
scrot screenshot.png || \
gnome-screenshot -f screenshot.png || \
import -window root screenshot.png || \
maim screenshot.png || \
echo "No screenshot tool available"

# Windows fallback
powershell -command "try { [screenshot command] } catch { Write-Error 'Screenshot failed' }"

# macOS fallback
screencapture screenshot.png || echo "Screenshot failed"
```

### Permission Handling
```bash
# Linux - check for sudo
if sudo -n true 2>/dev/null; then
    sudo shutdown -h now
else
    echo "Sudo access required"
fi

# Windows - check for admin
net session >nul 2>&1
if %errorLevel% == 0 (
    echo "Admin privileges available"
) else (
    echo "Admin privileges required"
)
```

## Security Considerations

1. **Command Injection Prevention**: Always validate and sanitize commands
2. **Privilege Escalation**: Be careful with sudo/admin requirements
3. **Path Traversal**: Validate file paths for screenshots
4. **Rate Limiting**: Implement delays between commands
5. **Audit Logging**: Log all executed commands

## Testing Commands

Use these commands to test platform detection and capability:

```bash
# Detect platform
uname -s          # Linux/Darwin
echo $OS          # Windows (if set)

# Test screenshot tools
which scrot gnome-screenshot import maim  # Linux
where nircmd      # Windows (if installed)
which screencapture  # macOS

# Test system commands
which shutdown reboot systemctl  # Linux/macOS
where shutdown    # Windows
```

This reference provides comprehensive cross-platform support for all remote control operations in your gaming cafe management system.
