# Gaming Cafe Chat System Implementation Guide

This guide explains how to implement the chat system in your server app to match the client app's chat functionality.

## Overview

The chat system uses PocketBase's `message` collection to enable real-time communication between clients and admin staff. The client app sends messages marked as `is_from_client: true`, while the server app sends messages marked as `is_from_client: false`.

## PocketBase Schema

The chat system uses the `message` collection with the following structure:

```javascript
{
  id: string,
  sender: string,        // relation to clients collection
  device: string,        // relation to devices collection  
  message: string,       // the actual message content
  is_from_client: boolean, // true for client messages, false for admin
  is_read: boolean,      // message read status
  created: string,       // auto-generated timestamp
  updated: string        // auto-generated timestamp
}
```

## Server App Implementation

### 1. Install PocketBase SDK

```bash
npm install pocketbase
# or
yarn add pocketbase
```

### 2. Initialize PocketBase Connection

```javascript
// lib/pocketbase.js
import PocketBase from 'pocketbase'

const pb = new PocketBase('http://127.0.0.1:8090') // Your PocketBase URL

export default pb
```

### 3. Chat Service Implementation

```javascript
// services/chatService.js
import pb from '../lib/pocketbase.js'

export class ChatService {
  // Get messages for a specific device
  static async getMessages(deviceId, limit = 50) {
    try {
      const messages = await pb.collection('message').getList(1, limit, {
        filter: `device = "${deviceId}"`,
        sort: '-created',
        expand: 'sender'
      })
      return messages.items
    } catch (error) {
      console.error('Failed to get messages:', error)
      return []
    }
  }

  // Send message from admin to client
  static async sendAdminMessage(deviceId, message, adminId = null) {
    try {
      const messageData = {
        sender: adminId, // Can be null for system messages
        device: deviceId,
        message: message,
        is_from_client: false,
        is_read: false
      }

      const newMessage = await pb.collection('message').create(messageData)
      return newMessage
    } catch (error) {
      console.error('Failed to send admin message:', error)
      throw error
    }
  }

  // Mark messages as read
  static async markMessagesAsRead(messageIds) {
    try {
      const promises = messageIds.map(id => 
        pb.collection('message').update(id, { is_read: true })
      )
      await Promise.all(promises)
    } catch (error) {
      console.error('Failed to mark messages as read:', error)
    }
  }

  // Get unread messages count for a device
  static async getUnreadCount(deviceId) {
    try {
      const result = await pb.collection('message').getList(1, 1, {
        filter: `device = "${deviceId}" && is_from_client = true && is_read = false`
      })
      return result.totalItems
    } catch (error) {
      console.error('Failed to get unread count:', error)
      return 0
    }
  }

  // Subscribe to real-time message updates
  static subscribeToMessages(deviceId, callback) {
    return pb.collection('message').subscribe('*', (e) => {
      if (e.record.device === deviceId) {
        callback(e.record)
      }
    })
  }

  // Get all active chat sessions (devices with recent messages)
  static async getActiveChatSessions() {
    try {
      const messages = await pb.collection('message').getList(1, 100, {
        sort: '-created',
        expand: 'device,sender'
      })

      // Group by device and get latest message for each
      const deviceMap = new Map()
      messages.items.forEach(message => {
        if (!deviceMap.has(message.device)) {
          deviceMap.set(message.device, {
            device: message.expand.device,
            lastMessage: message,
            unreadCount: 0
          })
        }
        
        // Count unread messages from clients
        if (message.is_from_client && !message.is_read) {
          deviceMap.get(message.device).unreadCount++
        }
      })

      return Array.from(deviceMap.values())
    } catch (error) {
      console.error('Failed to get active chat sessions:', error)
      return []
    }
  }
}
```

### 4. React Component Example

```jsx
// components/ChatPanel.jsx
import React, { useState, useEffect } from 'react'
import { ChatService } from '../services/chatService'

export default function ChatPanel({ selectedDeviceId }) {
  const [messages, setMessages] = useState([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (selectedDeviceId) {
      loadMessages()
      
      // Subscribe to real-time updates
      const unsubscribe = ChatService.subscribeToMessages(selectedDeviceId, (message) => {
        setMessages(prev => [message, ...prev])
      })

      return () => unsubscribe()
    }
  }, [selectedDeviceId])

  const loadMessages = async () => {
    const messageList = await ChatService.getMessages(selectedDeviceId)
    setMessages(messageList.reverse())
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || isLoading) return

    setIsLoading(true)
    try {
      await ChatService.sendAdminMessage(selectedDeviceId, newMessage.trim())
      setNewMessage('')
    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="chat-panel">
      <div className="messages">
        {messages.map(message => (
          <div 
            key={message.id} 
            className={`message ${message.is_from_client ? 'client' : 'admin'}`}
          >
            <div className="content">{message.message}</div>
            <div className="timestamp">
              {new Date(message.created).toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>
      
      <div className="input-area">
        <input
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="Type your message..."
          disabled={isLoading}
        />
        <button onClick={sendMessage} disabled={!newMessage.trim() || isLoading}>
          Send
        </button>
      </div>
    </div>
  )
}
```

### 5. Chat Session List Component

```jsx
// components/ChatSessionList.jsx
import React, { useState, useEffect } from 'react'
import { ChatService } from '../services/chatService'

export default function ChatSessionList({ onSelectDevice }) {
  const [sessions, setSessions] = useState([])

  useEffect(() => {
    loadSessions()
    
    // Refresh sessions every 30 seconds
    const interval = setInterval(loadSessions, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadSessions = async () => {
    const activeSessions = await ChatService.getActiveChatSessions()
    setSessions(activeSessions)
  }

  return (
    <div className="chat-sessions">
      <h3>Active Chats</h3>
      {sessions.map(session => (
        <div 
          key={session.device.id}
          className="session-item"
          onClick={() => onSelectDevice(session.device.id)}
        >
          <div className="device-name">{session.device.name}</div>
          <div className="last-message">{session.lastMessage.message}</div>
          {session.unreadCount > 0 && (
            <div className="unread-badge">{session.unreadCount}</div>
          )}
        </div>
      ))}
    </div>
  )
}
```

## Integration Tips

1. **Real-time Updates**: Use PocketBase's real-time subscriptions to get instant message updates
2. **Unread Indicators**: Show unread message counts to help admins prioritize responses
3. **Device Context**: Always associate messages with specific devices for proper routing
4. **Message History**: Load recent message history when opening a chat session
5. **Error Handling**: Implement proper error handling for network issues
6. **Notifications**: Consider adding browser notifications for new client messages

## Security Considerations

1. **Authentication**: Ensure admin users are properly authenticated before accessing chat
2. **Authorization**: Implement proper access controls for message viewing/sending
3. **Rate Limiting**: Consider implementing rate limiting to prevent spam
4. **Message Validation**: Validate and sanitize message content before storing

## Testing

1. Test real-time message delivery between client and server apps
2. Verify unread message counts update correctly
3. Test message persistence and history loading
4. Ensure proper error handling for network failures
5. Test with multiple concurrent chat sessions

This implementation provides a robust foundation for the chat system that matches your client app's functionality while providing the admin interface needed for customer support.
