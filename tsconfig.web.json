{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/env.d.ts", "src/renderer/src/**/*", "src/renderer/src/**/*.tsx", "src/preload/*.d.ts"], "compilerOptions": {"composite": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@renderer/*": ["src/renderer/src/*"], "@/components/*": ["src/renderer/src/components/*"], "@/lib/*": ["src/renderer/src/lib/*"], "@/hooks/*": ["src/renderer/src/hooks/*"], "@/types/*": ["src/renderer/src/types/*"], "@/pages/*": ["src/renderer/src/pages/*"]}}}