# Server App Remote Control Implementation Guide

This guide shows how to implement remote control functionality in your server app to control client PCs.

## Overview

The remote control system works by:
1. Server app creates commands in PocketBase `remote_commands` collection
2. Client apps listen for commands via real-time subscriptions
3. Client apps execute commands and update status
4. Server app monitors command execution and results

## Installation

```bash
npm install pocketbase
# or
yarn add pocketbase
```

## Core Service Implementation

### 1. Remote Control Service

```javascript
// services/remoteControlService.js
import pb from '../lib/pocketbase.js'

export class RemoteControlService {
  // Send screenshot command to device
  static async requestScreenshot(deviceId, adminId) {
    try {
      const command = await pb.collection('remote_commands').create({
        target_device: deviceId,
        command: 'Screenshot',
        created_by: adminId,
        status: 'pending'
      })

      return command
    } catch (error) {
      console.error('Failed to request screenshot:', error)
      throw error
    }
  }

  // Send system control command
  static async sendSystemCommand(deviceId, command, adminId) {
    const validCommands = ['Shutdown', 'Reboot', 'Sleep', 'Lock', 'Unlock']
    
    if (!validCommands.includes(command)) {
      throw new Error(`Invalid command: ${command}`)
    }

    try {
      const remoteCommand = await pb.collection('remote_commands').create({
        target_device: deviceId,
        command: command,
        created_by: adminId,
        status: 'pending'
      })

      return remoteCommand
    } catch (error) {
      console.error('Failed to send system command:', error)
      throw error
    }
  }

  // Get command status
  static async getCommandStatus(commandId) {
    try {
      return await pb.collection('remote_commands').getOne(commandId)
    } catch (error) {
      console.error('Failed to get command status:', error)
      throw error
    }
  }

  // Get recent commands for a device
  static async getDeviceCommands(deviceId, limit = 20) {
    try {
      const commands = await pb.collection('remote_commands').getList(1, limit, {
        filter: `target_device = "${deviceId}"`,
        sort: '-created',
        expand: 'created_by'
      })

      return commands.items
    } catch (error) {
      console.error('Failed to get device commands:', error)
      return []
    }
  }

  // Get latest screenshot for device
  static async getLatestScreenshot(deviceId) {
    try {
      const screenshots = await pb.collection('screenshots').getList(1, 1, {
        filter: `device = "${deviceId}"`,
        sort: '-taken_at'
      })

      return screenshots.items[0] || null
    } catch (error) {
      console.error('Failed to get latest screenshot:', error)
      return null
    }
  }

  // Get all screenshots for device
  static async getDeviceScreenshots(deviceId, limit = 10) {
    try {
      const screenshots = await pb.collection('screenshots').getList(1, limit, {
        filter: `device = "${deviceId}"`,
        sort: '-taken_at'
      })

      return screenshots.items
    } catch (error) {
      console.error('Failed to get device screenshots:', error)
      return []
    }
  }

  // Subscribe to command updates
  static subscribeToCommandUpdates(commandId, callback) {
    return pb.collection('remote_commands').subscribe(commandId, callback)
  }

  // Subscribe to new screenshots
  static subscribeToScreenshots(deviceId, callback) {
    return pb.collection('screenshots').subscribe('*', (e) => {
      if (e.record.device === deviceId) {
        callback(e.record)
      }
    })
  }

  // Get screenshot URL
  static getScreenshotUrl(screenshot) {
    return pb.files.getUrl(screenshot, screenshot.image)
  }

  // Delete old screenshots (cleanup)
  static async cleanupOldScreenshots(deviceId, keepCount = 10) {
    try {
      const screenshots = await pb.collection('screenshots').getList(1, 100, {
        filter: `device = "${deviceId}"`,
        sort: '-taken_at'
      })

      if (screenshots.items.length > keepCount) {
        const toDelete = screenshots.items.slice(keepCount)
        
        for (const screenshot of toDelete) {
          await pb.collection('screenshots').delete(screenshot.id)
        }
      }
    } catch (error) {
      console.error('Failed to cleanup screenshots:', error)
    }
  }
}
```

### 2. React Component for Device Control

```jsx
// components/DeviceControl.jsx
import React, { useState, useEffect } from 'react'
import { RemoteControlService } from '../services/remoteControlService'

export default function DeviceControl({ device, currentUser }) {
  const [latestScreenshot, setLatestScreenshot] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [commandHistory, setCommandHistory] = useState([])

  useEffect(() => {
    loadLatestScreenshot()
    loadCommandHistory()

    // Subscribe to new screenshots
    const unsubscribeScreenshots = RemoteControlService.subscribeToScreenshots(
      device.id, 
      (screenshot) => {
        setLatestScreenshot(screenshot)
      }
    )

    return () => {
      unsubscribeScreenshots()
    }
  }, [device.id])

  const loadLatestScreenshot = async () => {
    const screenshot = await RemoteControlService.getLatestScreenshot(device.id)
    setLatestScreenshot(screenshot)
  }

  const loadCommandHistory = async () => {
    const commands = await RemoteControlService.getDeviceCommands(device.id)
    setCommandHistory(commands)
  }

  const handleScreenshot = async () => {
    setIsLoading(true)
    try {
      const command = await RemoteControlService.requestScreenshot(device.id, currentUser.id)
      
      // Subscribe to command completion
      const unsubscribe = RemoteControlService.subscribeToCommandUpdates(command.id, (e) => {
        if (e.record.status === 'executed') {
          loadLatestScreenshot()
          loadCommandHistory()
          unsubscribe()
        }
        setIsLoading(false)
      })

      // Timeout after 30 seconds
      setTimeout(() => {
        setIsLoading(false)
        unsubscribe()
      }, 30000)

    } catch (error) {
      console.error('Screenshot request failed:', error)
      setIsLoading(false)
    }
  }

  const handleSystemCommand = async (command) => {
    if (!confirm(`Are you sure you want to ${command.toLowerCase()} this device?`)) {
      return
    }

    try {
      await RemoteControlService.sendSystemCommand(device.id, command, currentUser.id)
      loadCommandHistory()
    } catch (error) {
      console.error('System command failed:', error)
      alert('Command failed: ' + error.message)
    }
  }

  return (
    <div className="device-control">
      <h3>Device Control: {device.name}</h3>
      
      {/* Screenshot Section */}
      <div className="screenshot-section">
        <h4>Screen Monitoring</h4>
        <button 
          onClick={handleScreenshot} 
          disabled={isLoading}
          className="screenshot-btn"
        >
          {isLoading ? 'Taking Screenshot...' : 'Take Screenshot'}
        </button>
        
        {latestScreenshot && (
          <div className="screenshot-display">
            <img 
              src={RemoteControlService.getScreenshotUrl(latestScreenshot)}
              alt="Latest Screenshot"
              style={{ maxWidth: '100%', height: 'auto' }}
            />
            <p>Taken: {new Date(latestScreenshot.taken_at).toLocaleString()}</p>
          </div>
        )}
      </div>

      {/* System Control Section */}
      <div className="system-control">
        <h4>System Control</h4>
        <div className="control-buttons">
          <button 
            onClick={() => handleSystemCommand('Lock')}
            className="control-btn lock-btn"
          >
            🔒 Lock Screen
          </button>
          <button 
            onClick={() => handleSystemCommand('Sleep')}
            className="control-btn sleep-btn"
          >
            😴 Sleep
          </button>
          <button 
            onClick={() => handleSystemCommand('Reboot')}
            className="control-btn reboot-btn"
          >
            🔄 Reboot
          </button>
          <button 
            onClick={() => handleSystemCommand('Shutdown')}
            className="control-btn shutdown-btn"
          >
            ⚡ Shutdown
          </button>
        </div>
      </div>

      {/* Command History */}
      <div className="command-history">
        <h4>Recent Commands</h4>
        <div className="command-list">
          {commandHistory.map(command => (
            <div key={command.id} className={`command-item ${command.status}`}>
              <span className="command-name">{command.command}</span>
              <span className="command-status">{command.status}</span>
              <span className="command-time">
                {new Date(command.created).toLocaleString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
```

### 3. Device List with Remote Control

```jsx
// components/DeviceList.jsx
import React, { useState, useEffect } from 'react'
import DeviceControl from './DeviceControl'
import pb from '../lib/pocketbase'

export default function DeviceList({ currentUser }) {
  const [devices, setDevices] = useState([])
  const [selectedDevice, setSelectedDevice] = useState(null)

  useEffect(() => {
    loadDevices()
  }, [])

  const loadDevices = async () => {
    try {
      const deviceList = await pb.collection('devices').getFullList({
        expand: 'group'
      })
      setDevices(deviceList)
    } catch (error) {
      console.error('Failed to load devices:', error)
    }
  }

  return (
    <div className="device-management">
      <div className="device-list">
        <h2>Devices</h2>
        {devices.map(device => (
          <div 
            key={device.id} 
            className={`device-item ${device.status.toLowerCase()}`}
            onClick={() => setSelectedDevice(device)}
          >
            <div className="device-info">
              <h3>{device.name}</h3>
              <p>Type: {device.type}</p>
              <p>Status: {device.status}</p>
              {device.ip_address && <p>IP: {device.ip_address}</p>}
            </div>
            <div className="device-actions">
              <button onClick={() => setSelectedDevice(device)}>
                Control
              </button>
            </div>
          </div>
        ))}
      </div>

      {selectedDevice && (
        <div className="device-control-panel">
          <DeviceControl 
            device={selectedDevice} 
            currentUser={currentUser}
          />
        </div>
      )}
    </div>
  )
}
```

## CSS Styling

```css
/* styles/deviceControl.css */
.device-control {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 10px;
}

.screenshot-section {
  margin-bottom: 20px;
}

.screenshot-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.screenshot-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.screenshot-display {
  margin-top: 10px;
  text-align: center;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.lock-btn { background: #ffc107; }
.sleep-btn { background: #6c757d; color: white; }
.reboot-btn { background: #fd7e14; color: white; }
.shutdown-btn { background: #dc3545; color: white; }

.command-list {
  max-height: 200px;
  overflow-y: auto;
}

.command-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  border-bottom: 1px solid #eee;
}

.command-item.executed { background: #d4edda; }
.command-item.failed { background: #f8d7da; }
.command-item.pending { background: #fff3cd; }
```

## Security Best Practices

1. **Authentication**: Always verify admin/staff permissions
2. **Command Validation**: Validate all commands before execution
3. **Rate Limiting**: Implement rate limiting for commands
4. **Audit Logging**: Log all remote control activities
5. **Confirmation Dialogs**: Require confirmation for destructive actions

This implementation provides a complete remote control system that works seamlessly with your client app!
